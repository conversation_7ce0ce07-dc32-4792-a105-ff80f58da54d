# Contact Type Migration Script

## Overview

This script migrates contact types based on their associated opportunities. It ensures that contacts are properly categorized as either "prospect" or "client" based on the status of their opportunities.

## Migration Logic

The script follows this logic for each contact:

1. **Find all opportunities** for the contact (non-deleted)
2. **Determine new type** based on opportunity data:
   - **CLIENT**: If any opportunity has both `saleDate` AND `orderId`
   - **PROSPECT**: If contact has opportunities but none have both `saleDate` and `orderId`
   - **NO CHANGE**: If contact has no opportunities, the type is not updated

## What the Script Does

### 1. Contact Discovery
- Finds all non-deleted contacts in the database
- Processes each contact individually

### 2. Opportunity Analysis
For each contact:
- Looks up all non-deleted opportunities linked to the contact
- Examines each opportunity for `saleDate` and `orderId` fields
- Determines the highest status (client > prospect)

### 3. Type Update Logic
- **Upgrade to CLIENT**: If any opportunity is "sold" (has both saleDate and orderId)
- **Set to PROSPECT**: If opportunities exist but none are "sold"
- **Skip**: If no opportunities exist for the contact

### 4. Safety Features
- **Dry Run Mode**: Default mode that shows what would be changed without making updates
- **Detailed Logging**: Shows the decision process for each contact
- **Error Handling**: Continues processing other contacts if one fails
- **Summary Report**: Provides counts of all changes made

## Usage

### 1. Review the Script
```bash
cat scripts/migrate-contact-types-by-opportunity.js
```

### 2. Run in Dry Run Mode (Default)
```bash
node scripts/migrate-contact-types-by-opportunity.js
```

This will show you exactly what changes would be made without actually updating the database.

### 3. Apply Changes
Once you're satisfied with the dry run results:

1. Edit the script and change `DRY_RUN = true` to `DRY_RUN = false`
2. Run the script again:
```bash
node scripts/migrate-contact-types-by-opportunity.js
```

## Expected Output

### Dry Run Output Example
```
--- Processing Contact: John Doe (abc123) ---
Current type: other
Found 2 opportunities for this contact
  Opportunity opp1: saleDate=YES, orderId=YES
  Opportunity opp2: saleDate=NO, orderId=NO
🔄 Should be CLIENT (has opportunity with saleDate and orderId)
🔄 Need to update type from "other" to "client"
✅ Would update contact abc123 type to client
```

### Summary Example
```
=== MIGRATION SUMMARY ===
Total contacts processed: 1500
Contacts that would be updated to PROSPECT: 450
Contacts that would be updated to CLIENT: 200
Contacts already correct: 300
Contacts with no opportunities (skipped): 550
Contacts with errors: 0
```

## Safety Considerations

1. **Backup First**: Always backup your database before running migrations
2. **Test Environment**: Run this on a test environment first
3. **Dry Run**: Always review the dry run output before applying changes
4. **Monitor**: Watch the logs during execution for any errors

## Contact Type Enum Values

The script uses these contact type values:
- `"prospect"` - Contact with opportunities but no completed sales
- `"client"` - Contact with at least one completed sale (saleDate + orderId)

Other contact types (lead, vendor, etc.) are not modified by this script.

## Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure your `.env` file has the correct `MONGO_URI`
   - Check database connectivity

2. **No Changes Made**
   - Verify `DRY_RUN = false` if you want to apply changes
   - Check that contacts have opportunities in the database

3. **Script Hangs**
   - Large datasets may take time to process
   - Monitor the console output for progress

### Getting Help

If you encounter issues:
1. Check the console output for specific error messages
2. Verify your database connection and permissions
3. Ensure the Contact and Opportunity collections exist and have the expected fields

## Rollback

If you need to rollback changes, you would need to:
1. Identify the contacts that were changed (check the script output logs)
2. Manually update their types back to the original values
3. Consider creating a backup before running the migration to make rollback easier
