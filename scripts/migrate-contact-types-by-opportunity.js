/**
 * <PERSON><PERSON><PERSON> to migrate contact types based on their opportunities
 * 
 * This script updates contact types based on the following logic:
 * - If contact has opportunity but no saleDate or orderId → type: "prospect"
 * - If contact has opportunity with both saleDate and orderId → type: "client"
 * - If contact has no opportunity → don't update type
 * 
 * Usage:
 * 1. Review this script carefully
 * 2. Set DRY_RUN to false when ready to apply changes
 * 3. Run: node scripts/migrate-contact-types-by-opportunity.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Set to false to actually apply the updates
const DRY_RUN = true;

// MongoDB connection
const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('MongoDB connection error:', error);
        process.exit(1);
    }
};

// Define schemas (simplified versions)
const contactSchema = new mongoose.Schema({
    _id: String,
    fullName: String,
    firstName: String,
    lastName: String,
    phone: String,
    email: String,
    companyId: String,
    type: String,
    deleted: Boolean
}, { collection: 'Contact', timestamps: true });

const opportunitySchema = new mongoose.Schema({
    _id: String,
    contactId: String,
    companyId: String,
    saleDate: Date,
    orderId: String,
    deleted: Boolean
}, { collection: 'Opportunity', timestamps: true });

const Contact = mongoose.model('Contact', contactSchema);
const Opportunity = mongoose.model('Opportunity', opportunitySchema);

const migrateContactTypes = async () => {
    try {
        console.log("Starting contact type migration based on opportunities");
        console.log(`DRY RUN MODE: ${DRY_RUN ? 'ON' : 'OFF'}`);
        
        let prospectCount = 0;
        let clientCount = 0;
        let noOpportunityCount = 0;
        let alreadyCorrectCount = 0;
        let errorCount = 0;
        
        // Get all non-deleted contacts
        const contacts = await Contact.find({
            deleted: { $ne: true }
        }).select('_id fullName firstName lastName type companyId').lean();
        
        console.log(`Found ${contacts.length} contacts to process`);
        
        for (const contact of contacts) {
            try {
                console.log(`\n--- Processing Contact: ${contact.fullName || contact.firstName} (${contact._id}) ---`);
                console.log(`Current type: ${contact.type}`);
                
                // Find opportunities for this contact
                const opportunities = await Opportunity.find({
                    contactId: contact._id,
                    companyId: contact.companyId,
                    deleted: { $ne: true }
                }).select('_id saleDate orderId').lean();
                
                console.log(`Found ${opportunities.length} opportunities for this contact`);
                
                if (opportunities.length === 0) {
                    console.log(`❌ No opportunities found - skipping type update`);
                    noOpportunityCount++;
                    continue;
                }
                
                // Determine the new type based on opportunities
                let newType = null;
                let hasClientOpportunity = false;
                
                for (const opp of opportunities) {
                    console.log(`  Opportunity ${opp._id}: saleDate=${opp.saleDate ? 'YES' : 'NO'}, orderId=${opp.orderId ? 'YES' : 'NO'}`);
                    
                    // If any opportunity has both saleDate and orderId, contact should be client
                    if (opp.saleDate && opp.orderId) {
                        hasClientOpportunity = true;
                        break;
                    }
                }
                
                if (hasClientOpportunity) {
                    newType = 'client';
                    console.log(`🔄 Should be CLIENT (has opportunity with saleDate and orderId)`);
                } else {
                    newType = 'prospect';
                    console.log(`🔄 Should be PROSPECT (has opportunity but no saleDate/orderId)`);
                }
                
                // Check if type is already correct
                if (contact.type === newType) {
                    console.log(`✓ Type is already correct (${newType})`);
                    alreadyCorrectCount++;
                    continue;
                }
                
                console.log(`🔄 Need to update type from "${contact.type}" to "${newType}"`);
                
                if (!DRY_RUN) {
                    await Contact.updateOne(
                        { _id: contact._id },
                        { $set: { type: newType } }
                    );
                    console.log(`✅ Updated contact ${contact._id} type to ${newType}`);
                } else {
                    console.log(`✅ Would update contact ${contact._id} type to ${newType}`);
                }
                
                if (newType === 'prospect') {
                    prospectCount++;
                } else if (newType === 'client') {
                    clientCount++;
                }
                
            } catch (contactError) {
                console.error(`❌ Error processing contact ${contact._id}:`, contactError.message);
                errorCount++;
            }
        }
        
        console.log("\n=== MIGRATION SUMMARY ===");
        console.log(`Total contacts processed: ${contacts.length}`);
        console.log(`Contacts ${DRY_RUN ? 'that would be' : ''} updated to PROSPECT: ${prospectCount}`);
        console.log(`Contacts ${DRY_RUN ? 'that would be' : ''} updated to CLIENT: ${clientCount}`);
        console.log(`Contacts already correct: ${alreadyCorrectCount}`);
        console.log(`Contacts with no opportunities (skipped): ${noOpportunityCount}`);
        console.log(`Contacts with errors: ${errorCount}`);
        
        if (DRY_RUN) {
            console.log("\n⚠️  THIS WAS A DRY RUN - NO ACTUAL UPDATES WERE MADE");
            console.log("⚠️  SET DRY_RUN = false TO APPLY CHANGES");
        } else {
            console.log("\n✅ MIGRATION COMPLETED - UPDATES HAVE BEEN APPLIED TO THE DATABASE");
        }
        
    } catch (error) {
        console.error("Contact type migration failed:", error);
        throw error;
    }
};

const main = async () => {
    try {
        await connectDB();
        await migrateContactTypes();
    } catch (error) {
        console.error('Migration script failed:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
        process.exit(0);
    }
};

// Run the script
main();
